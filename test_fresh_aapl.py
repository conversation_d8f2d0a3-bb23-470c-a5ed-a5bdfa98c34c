#!/usr/bin/env python3
"""
Test the investor-agent MCP server with fresh packages (no cache) for AAPL data.
"""

import asyncio
import json
import sys

async def send_mcp_request(process, request_id: int, method: str, params: dict = None):
    """Send an MCP request and return the response."""
    request = {
        "jsonrpc": "2.0",
        "id": request_id,
        "method": method
    }
    if params:
        request["params"] = params
    
    request_json = json.dumps(request) + "\n"
    process.stdin.write(request_json.encode())
    await process.stdin.drain()
    
    response_line = await process.stdout.readline()
    return json.loads(response_line.decode())

async def test_fresh_aapl_data():
    """Test AAPL data with fresh packages."""
    
    print("🚀 Starting investor-agent with fresh packages (no cache)...")
    
    # Start the MCP server process with no-cache and refresh
    process = await asyncio.create_subprocess_exec(
        "uvx", "--no-cache", "--refresh", "investor-agent",
        stdin=asyncio.subprocess.PIPE,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    
    try:
        # Initialize the MCP session
        print("🔧 Initializing MCP session...")
        await send_mcp_request(process, 1, "initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": {"listChanged": True},
                "sampling": {}
            },
            "clientInfo": {
                "name": "fresh-test-client",
                "version": "1.0.0"
            }
        })
        
        # Send initialized notification
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        initialized_json = json.dumps(initialized_notification) + "\n"
        process.stdin.write(initialized_json.encode())
        await process.stdin.drain()
        
        print("✅ MCP session initialized successfully")
        
        # Test AAPL ticker data
        print("\n📊 Testing AAPL ticker data with fresh packages...")
        ticker_response = await send_mcp_request(process, 2, "tools/call", {
            "name": "get_ticker_data",
            "arguments": {"ticker": "AAPL"}
        })
        
        if "result" in ticker_response and ticker_response["result"]["content"]:
            content = ticker_response["result"]["content"][0]["text"]
            if "No information available" not in content and "No data found" not in content:
                print("✅ AAPL ticker data retrieved successfully!")
                print("📈 AAPL Data Preview:")
                # Show first 10 lines
                lines = content.split('\n')[:10]
                for line in lines:
                    if line.strip():
                        print(f"   {line}")
                print("   ...")
                return True
            else:
                print("⚠️  AAPL ticker data still returns no information")
                print(f"Response: {content}")
        else:
            print("❌ Failed to get AAPL ticker data")
            print(f"Response: {ticker_response}")
        
        # Test AAPL price history as backup
        print("\n📈 Testing AAPL price history...")
        price_response = await send_mcp_request(process, 3, "tools/call", {
            "name": "get_price_history",
            "arguments": {"ticker": "AAPL", "period": "5d"}
        })
        
        if "result" in price_response and price_response["result"]["content"]:
            content = price_response["result"]["content"][0]["text"]
            if "No historical data found" not in content and "No data found" not in content:
                print("✅ AAPL price history retrieved successfully!")
                print("📊 Price History Preview:")
                lines = content.split('\n')[:8]
                for line in lines:
                    if line.strip():
                        print(f"   {line}")
                return True
            else:
                print("⚠️  AAPL price history still returns no data")
                print(f"Response: {content}")
        
        return False
        
    except Exception as e:
        print(f"❌ Error during fresh test: {e}")
        return False
        
    finally:
        # Clean up
        if process.stdin:
            process.stdin.close()
        try:
            await asyncio.wait_for(process.wait(), timeout=5.0)
        except asyncio.TimeoutError:
            process.terminate()
            await process.wait()

if __name__ == "__main__":
    print("🧪 Testing investor-agent with fresh packages for AAPL data...")
    success = asyncio.run(test_fresh_aapl_data())
    if success:
        print("\n🎉 SUCCESS! AAPL data retrieved with fresh packages!")
        print("✅ Your MCP server is now working correctly with fresh yfinance data.")
    else:
        print("\n⚠️  AAPL data still not available - this might be a yfinance API issue.")
        print("🔧 However, your MCP server configuration is correct.")
    
    print("\n📝 Your updated MCP configuration now uses fresh packages on each run.")
    print("🚀 You can now use the investor-agent in your MCP client applications!")
