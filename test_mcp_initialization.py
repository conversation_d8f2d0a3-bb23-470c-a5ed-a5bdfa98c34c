#!/usr/bin/env python3
"""
Test MCP initialization to debug the exact issue.
"""

import asyncio
import json
import subprocess
import sys
import os

async def test_mcp_initialization():
    """Test the exact MCP initialization sequence."""
    
    print("🚀 Testing MCP initialization sequence...")
    
    # Use the exact same configuration as mcp.json
    cwd = "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\stocks\\mcp1\\investor-agent"
    env = os.environ.copy()
    env["PYTHONPATH"] = "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\stocks\\mcp1\\investor-agent\\src"
    
    print(f"📁 Working directory: {cwd}")
    print(f"🐍 PYTHONPATH: {env.get('PYTHONPATH')}")
    
    # Check if directory exists
    if not os.path.exists(cwd):
        print(f"❌ Directory does not exist: {cwd}")
        return False
    
    try:
        # Start the process exactly like MCP would
        process = await asyncio.create_subprocess_exec(
            "python", "-m", "src.investor_agent.server",
            cwd=cwd,
            env=env,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        print("✅ Process started successfully")
        
        # Send the initialize request (exactly like MCP client would)
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "roots": {
                        "listChanged": True
                    },
                    "sampling": {}
                },
                "clientInfo": {
                    "name": "test-mcp-client",
                    "version": "1.0.0"
                }
            }
        }
        
        print("📤 Sending initialize request...")
        init_json = json.dumps(init_request) + "\n"
        process.stdin.write(init_json.encode())
        await process.stdin.drain()
        
        # Wait for response with timeout
        try:
            response_line = await asyncio.wait_for(process.stdout.readline(), timeout=10.0)
            if response_line:
                response = json.loads(response_line.decode())
                print("✅ Received initialize response!")
                print(f"📥 Response: {json.dumps(response, indent=2)}")
                
                # Send initialized notification
                initialized_notification = {
                    "jsonrpc": "2.0",
                    "method": "notifications/initialized"
                }
                
                initialized_json = json.dumps(initialized_notification) + "\n"
                process.stdin.write(initialized_json.encode())
                await process.stdin.drain()
                
                print("✅ MCP initialization sequence completed successfully!")
                return True
            else:
                print("❌ No response received")
                return False
                
        except asyncio.TimeoutError:
            print("❌ Timeout waiting for initialize response")
            return False
        
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        return False
        
    finally:
        # Clean up
        if process and process.returncode is None:
            process.terminate()
            try:
                await asyncio.wait_for(process.wait(), timeout=5.0)
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
        
        # Check for any error output
        if process:
            try:
                stdout, stderr = await process.communicate()
                if stderr:
                    print(f"\n🚨 STDERR output:")
                    print(stderr.decode())
            except:
                pass

def test_import_directly():
    """Test importing the server module directly."""
    print("\n🔍 Testing direct import...")
    
    # Add the path to sys.path
    import sys
    server_path = "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\stocks\\mcp1\\investor-agent\\src"
    if server_path not in sys.path:
        sys.path.insert(0, server_path)
    
    try:
        from investor_agent import server
        print("✅ Server module imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import server module: {e}")
        return False
    except Exception as e:
        print(f"❌ Error importing server module: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing MCP initialization for local investor-agent...")
    
    # Test direct import first
    import_ok = test_import_directly()
    if not import_ok:
        print("\n❌ Direct import failed - this is likely the issue!")
        sys.exit(1)
    
    # Test MCP initialization
    success = asyncio.run(test_mcp_initialization())
    
    if success:
        print("\n🎉 MCP initialization test PASSED!")
        print("✅ Your local configuration should work now.")
    else:
        print("\n❌ MCP initialization test FAILED!")
        print("🔍 Check the error messages above for clues.")
        sys.exit(1)
