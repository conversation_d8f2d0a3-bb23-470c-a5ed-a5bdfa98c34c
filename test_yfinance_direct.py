#!/usr/bin/env python3
"""
Direct test of yfinance to see if the issue is with the library itself or the MCP server.
"""

import sys

def test_yfinance_direct():
    """Test yfinance directly to isolate the issue."""
    
    try:
        print("📦 Installing yfinance...")
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "install", "yfinance[nospam]"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ Failed to install yfinance: {result.stderr}")
            return False
        
        print("✅ yfinance installed successfully")
        
        print("\n🔍 Testing yfinance directly with AAPL...")
        import yfinance as yf
        
        # Test basic ticker info
        ticker = yf.Ticker("AAPL")
        
        print("📊 Getting basic info...")
        info = ticker.info
        if info and len(info) > 1:  # yfinance returns {'trailingPegRatio': None} when no data
            print("✅ AAPL basic info retrieved successfully!")
            print(f"   Company: {info.get('longName', 'N/A')}")
            print(f"   Sector: {info.get('sector', 'N/A')}")
            print(f"   Current Price: ${info.get('currentPrice', 'N/A')}")
            print(f"   Market Cap: ${info.get('marketCap', 'N/A'):,}" if info.get('marketCap') else "   Market Cap: N/A")
        else:
            print("⚠️  AAPL basic info returned minimal data")
            print(f"   Info keys: {list(info.keys()) if info else 'None'}")
        
        print("\n📈 Getting price history...")
        hist = ticker.history(period="5d")
        if not hist.empty:
            print("✅ AAPL price history retrieved successfully!")
            print("   Recent prices:")
            print(hist.tail(3).to_string())
        else:
            print("⚠️  AAPL price history is empty")
        
        print("\n💰 Getting financial data...")
        financials = ticker.financials
        if not financials.empty:
            print("✅ AAPL financials retrieved successfully!")
            print(f"   Financials shape: {financials.shape}")
        else:
            print("⚠️  AAPL financials are empty")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing yfinance: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing yfinance directly to isolate the issue...")
    success = test_yfinance_direct()
    if success:
        print("\n✅ yfinance direct test completed!")
    else:
        print("\n❌ yfinance direct test failed!")
        sys.exit(1)
