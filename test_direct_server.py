#!/usr/bin/env python3
"""
Test running the server directly to see any startup errors.
"""

import subprocess
import sys
import os
import time

def test_direct_server():
    """Test running the server directly."""
    
    print("🚀 Testing direct server execution...")
    
    # Change to the investor-agent directory
    cwd = "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\stocks\\mcp1\\investor-agent"
    env = os.environ.copy()
    env["PYTHONPATH"] = "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\stocks\\mcp1\\investor-agent\\src"
    
    print(f"📁 Working directory: {cwd}")
    print(f"🐍 PYTHONPATH: {env.get('PYTHONPATH')}")
    
    try:
        # Start the server process
        print("🔄 Starting server process...")
        process = subprocess.Popen(
            ["python", "-m", "src.investor_agent.server"],
            cwd=cwd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("⏳ Waiting 5 seconds to see if server starts...")
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            print("✅ Server is running!")
            
            # Try to send a simple test
            print("📤 Sending test input...")
            try:
                process.stdin.write('{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {}}\n')
                process.stdin.flush()
            except:
                print("⚠️  Could not send test input (this is expected)")
            
            # Terminate the process
            print("🔄 Terminating server...")
            process.terminate()
            try:
                stdout, stderr = process.communicate(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                stdout, stderr = process.communicate()
            
            print("📄 Server output:")
            if stdout:
                print("STDOUT:")
                print(stdout)
            if stderr:
                print("STDERR:")
                print(stderr)
            
            return True
        else:
            # Process exited
            stdout, stderr = process.communicate()
            print("❌ Server exited immediately!")
            print(f"Exit code: {process.returncode}")
            
            if stdout:
                print("\n📄 STDOUT:")
                print(stdout)
            if stderr:
                print("\n🚨 STDERR:")
                print(stderr)
            
            return False
            
    except Exception as e:
        print(f"❌ Error running server: {e}")
        return False

if __name__ == "__main__":
    success = test_direct_server()
    
    if success:
        print("\n✅ Server can start successfully!")
        print("🔧 The issue might be with MCP client communication.")
    else:
        print("\n❌ Server failed to start!")
        print("🔍 Check the error messages above.")
