#!/usr/bin/env python3
"""
Comprehensive test of the investor-agent MCP server with AAPL stock data.
Tests multiple tools to ensure functionality.
"""

import asyncio
import json
import sys
from typing import Any, Dict

async def send_mcp_request(process, request_id: int, method: str, params: Dict[str, Any] = None):
    """Send an MCP request and return the response."""
    request = {
        "jsonrpc": "2.0",
        "id": request_id,
        "method": method
    }
    if params:
        request["params"] = params
    
    request_json = json.dumps(request) + "\n"
    process.stdin.write(request_json.encode())
    await process.stdin.drain()
    
    response_line = await process.stdout.readline()
    return json.loads(response_line.decode())

async def test_investor_agent_comprehensive():
    """Test multiple tools with AAPL stock data."""
    
    # Start the MCP server process
    process = await asyncio.create_subprocess_exec(
        "uvx", "investor-agent",
        stdin=asyncio.subprocess.PIPE,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    
    try:
        print("🚀 Starting investor-agent MCP server...")
        
        # Initialize the MCP session
        init_response = await send_mcp_request(process, 1, "initialize", {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "roots": {"listChanged": True},
                "sampling": {}
            },
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        })
        
        print("✅ Server initialized successfully")
        
        # Send initialized notification
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        initialized_json = json.dumps(initialized_notification) + "\n"
        process.stdin.write(initialized_json.encode())
        await process.stdin.drain()
        
        # Test 1: Get ticker data for AAPL
        print("\n📊 Testing get_ticker_data for AAPL...")
        ticker_response = await send_mcp_request(process, 2, "tools/call", {
            "name": "get_ticker_data",
            "arguments": {"ticker": "AAPL"}
        })
        
        if "result" in ticker_response and ticker_response["result"]["content"]:
            content = ticker_response["result"]["content"][0]["text"]
            if "No information available" not in content:
                print("✅ AAPL ticker data retrieved successfully")
                print(f"📈 Data preview: {content[:200]}...")
            else:
                print("⚠️  AAPL ticker data returned 'No information available'")
        else:
            print("❌ Failed to get AAPL ticker data")
        
        # Test 2: Get price history for AAPL
        print("\n📈 Testing get_price_history for AAPL...")
        price_response = await send_mcp_request(process, 3, "tools/call", {
            "name": "get_price_history",
            "arguments": {"ticker": "AAPL", "period": "1mo"}
        })
        
        if "result" in price_response and price_response["result"]["content"]:
            content = price_response["result"]["content"][0]["text"]
            if "No information available" not in content:
                print("✅ AAPL price history retrieved successfully")
                print(f"📊 Data preview: {content[:200]}...")
            else:
                print("⚠️  AAPL price history returned 'No information available'")
        else:
            print("❌ Failed to get AAPL price history")
        
        # Test 3: Get current Fear & Greed Index
        print("\n😨 Testing get_current_fng_tool...")
        fng_response = await send_mcp_request(process, 4, "tools/call", {
            "name": "get_current_fng_tool",
            "arguments": {}
        })
        
        if "result" in fng_response and fng_response["result"]["content"]:
            content = fng_response["result"]["content"][0]["text"]
            print("✅ Fear & Greed Index retrieved successfully")
            print(f"😱 Current F&G Index: {content}")
        else:
            print("❌ Failed to get Fear & Greed Index")
        
        # Test 4: Get financial statements for AAPL
        print("\n💰 Testing get_financial_statements for AAPL...")
        financial_response = await send_mcp_request(process, 5, "tools/call", {
            "name": "get_financial_statements",
            "arguments": {"ticker": "AAPL", "statement_type": "income", "frequency": "quarterly"}
        })
        
        if "result" in financial_response and financial_response["result"]["content"]:
            content = financial_response["result"]["content"][0]["text"]
            if "No information available" not in content:
                print("✅ AAPL financial statements retrieved successfully")
                print(f"💸 Data preview: {content[:200]}...")
            else:
                print("⚠️  AAPL financial statements returned 'No information available'")
        else:
            print("❌ Failed to get AAPL financial statements")
        
        print("\n🎉 Comprehensive test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during comprehensive test: {e}")
        return False
        
    finally:
        # Clean up
        if process.stdin:
            process.stdin.close()
        try:
            await asyncio.wait_for(process.wait(), timeout=5.0)
        except asyncio.TimeoutError:
            process.terminate()
            await process.wait()

if __name__ == "__main__":
    print("🧪 Running comprehensive investor-agent MCP server test with AAPL...")
    success = asyncio.run(test_investor_agent_comprehensive())
    if success:
        print("\n✅ All tests completed successfully!")
        print("\n🔧 Your MCP configuration is working correctly!")
        print("📝 You can now use the investor-agent in your MCP client.")
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
