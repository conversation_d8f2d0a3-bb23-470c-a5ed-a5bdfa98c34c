#!/usr/bin/env python3
"""
Simple test to check if the investor-agent server can start properly.
"""

import subprocess
import sys
import time
import os

def test_server_start():
    """Test if the server can start properly."""
    print("🚀 Testing server startup...")
    
    # Check if the investor-agent directory exists
    if not os.path.exists("investor-agent"):
        print("❌ investor-agent directory not found!")
        print(f"Current directory: {os.getcwd()}")
        print("Directory contents:")
        for item in os.listdir("."):
            print(f"  {item}")
        return False
    
    print("✅ investor-agent directory found")
    
    # Check if the server module exists
    server_path = os.path.join("investor-agent", "src", "investor_agent", "server.py")
    if not os.path.exists(server_path):
        print(f"❌ Server module not found at: {server_path}")
        return False
    
    print("✅ Server module found")
    
    try:
        # Start the server process
        print("🔄 Starting server process...")
        process = subprocess.Popen(
            ["python", "-m", "src.investor_agent.server"],
            cwd="investor-agent",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a few seconds to see if it stays running
        print("⏳ Waiting 3 seconds to check if server stays running...")
        time.sleep(3)
        
        # Check if the process is still running
        if process.poll() is None:
            print("✅ Server started successfully and is still running!")
            print("🔄 Terminating server...")
            # Send CTRL+C to terminate
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
        else:
            # Process exited, get the error output
            stdout, stderr = process.communicate()
            print("❌ Server exited immediately!")
            print(f"Exit code: {process.returncode}")
            if stdout:
                print("\n📄 STDOUT:")
                print(stdout)
            if stderr:
                print("\n🚨 STDERR:")
                print(stderr)
            return False
            
    except FileNotFoundError as e:
        print(f"❌ Python or module not found: {e}")
        return False
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are available."""
    print("\n🔍 Checking dependencies...")
    
    try:
        import yfinance
        print("✅ yfinance imported successfully")
    except ImportError as e:
        print(f"❌ yfinance import failed: {e}")
        return False
    
    try:
        import mcp
        print("✅ mcp imported successfully")
    except ImportError as e:
        print(f"❌ mcp import failed: {e}")
        return False
    
    try:
        import httpx
        print("✅ httpx imported successfully")
    except ImportError as e:
        print(f"❌ httpx import failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 Testing investor-agent MCP server startup...")
    print(f"📁 Current working directory: {os.getcwd()}")
    
    # Check dependencies first
    deps_ok = check_dependencies()
    if not deps_ok:
        print("\n❌ Dependencies check failed!")
        print("💡 Try running: cd investor-agent && pip install -e .")
        sys.exit(1)
    
    # Test server startup
    success = test_server_start()
    
    if success:
        print("\n🎉 Server startup test PASSED!")
        print("✅ Your MCP configuration should work now.")
    else:
        print("\n❌ Server startup test FAILED!")
        print("\n🔍 Troubleshooting suggestions:")
        print("1. Make sure you're in the correct directory")
        print("2. Try: cd investor-agent && pip install -e .")
        print("3. Check Python version (requires 3.12+)")
        print("4. Try using uvx instead of python in mcp.json")
        sys.exit(1)
