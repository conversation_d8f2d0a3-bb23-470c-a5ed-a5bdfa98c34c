#!/usr/bin/env python3
"""
Test script to verify the investor-agent MCP server is working correctly.
This script will test the get_ticker_data tool with AAPL stock.
"""

import asyncio
import json
import subprocess
import sys
from typing import Any, Dict

async def test_mcp_server():
    """Test the MCP server by calling the get_ticker_data tool with AAPL."""
    
    # Start the MCP server process
    process = await asyncio.create_subprocess_exec(
        "uvx", "investor-agent",
        stdin=asyncio.subprocess.PIPE,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    
    try:
        # Initialize the MCP session
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "roots": {
                        "listChanged": True
                    },
                    "sampling": {}
                },
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        # Send initialization request
        init_json = json.dumps(init_request) + "\n"
        process.stdin.write(init_json.encode())
        await process.stdin.drain()
        
        # Read initialization response
        response_line = await process.stdout.readline()
        init_response = json.loads(response_line.decode())
        print("Initialization response:", json.dumps(init_response, indent=2))
        
        # Send initialized notification
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        initialized_json = json.dumps(initialized_notification) + "\n"
        process.stdin.write(initialized_json.encode())
        await process.stdin.drain()
        
        # List available tools
        list_tools_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        list_tools_json = json.dumps(list_tools_request) + "\n"
        process.stdin.write(list_tools_json.encode())
        await process.stdin.drain()
        
        # Read tools list response
        tools_response_line = await process.stdout.readline()
        tools_response = json.loads(tools_response_line.decode())
        print("Available tools:", json.dumps(tools_response, indent=2))
        
        # Test get_ticker_data with AAPL
        ticker_request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "get_ticker_data",
                "arguments": {
                    "ticker": "AAPL"
                }
            }
        }
        
        ticker_json = json.dumps(ticker_request) + "\n"
        process.stdin.write(ticker_json.encode())
        await process.stdin.drain()
        
        # Read ticker data response
        ticker_response_line = await process.stdout.readline()
        ticker_response = json.loads(ticker_response_line.decode())
        print("AAPL ticker data response:", json.dumps(ticker_response, indent=2))
        
        return True
        
    except Exception as e:
        print(f"Error testing MCP server: {e}")
        return False
        
    finally:
        # Clean up
        if process.stdin:
            process.stdin.close()
        try:
            await asyncio.wait_for(process.wait(), timeout=5.0)
        except asyncio.TimeoutError:
            process.terminate()
            await process.wait()

if __name__ == "__main__":
    print("Testing investor-agent MCP server...")
    success = asyncio.run(test_mcp_server())
    if success:
        print("✅ MCP server test completed successfully!")
    else:
        print("❌ MCP server test failed!")
        sys.exit(1)
