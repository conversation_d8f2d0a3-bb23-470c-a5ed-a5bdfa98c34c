#!/usr/bin/env python3
"""
Debug the difference between yfinance .info and .get_info() methods.
"""

import yfinance as yf

def debug_yfinance_methods():
    """Compare different yfinance methods for AAPL."""
    
    print("🔍 Debugging yfinance methods for AAPL...")
    
    ticker = yf.Ticker("AAPL")
    
    print("\n1️⃣ Testing .info property:")
    try:
        info = ticker.info
        print(f"   Type: {type(info)}")
        print(f"   Length: {len(info) if info else 0}")
        if info:
            print(f"   Sample keys: {list(info.keys())[:5]}")
            print(f"   Company: {info.get('longName', 'N/A')}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n2️⃣ Testing .get_info() method:")
    try:
        get_info = ticker.get_info()
        print(f"   Type: {type(get_info)}")
        print(f"   Length: {len(get_info) if get_info else 0}")
        if get_info:
            print(f"   Sample keys: {list(get_info.keys())[:5]}")
            print(f"   Company: {get_info.get('longName', 'N/A')}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n3️⃣ Testing .history() method:")
    try:
        hist = ticker.history(period="5d")
        print(f"   Type: {type(hist)}")
        print(f"   Shape: {hist.shape if not hist.empty else 'Empty'}")
        if not hist.empty:
            print(f"   Latest close: ${hist['Close'].iloc[-1]:.2f}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n4️⃣ Testing with session (like investor-agent):")
    try:
        from requests import Session
        from pyrate_limiter import Duration, RequestRate, Limiter
        from requests_cache import CacheMixin, SQLiteCache
        from requests_ratelimiter import LimiterMixin, MemoryQueueBucket
        
        class CachedLimiterSession(CacheMixin, LimiterMixin, Session):
            pass
        
        session = CachedLimiterSession(
            limiter=Limiter(RequestRate(5, Duration.SECOND)),
            bucket_class=MemoryQueueBucket,
            backend=SQLiteCache("test_yfinance.cache", expire_after=3600),
        )
        
        ticker_with_session = yf.Ticker("AAPL", session=session)
        
        print("   Testing .get_info() with session:")
        get_info_session = ticker_with_session.get_info()
        print(f"   Type: {type(get_info_session)}")
        print(f"   Length: {len(get_info_session) if get_info_session else 0}")
        if get_info_session:
            print(f"   Company: {get_info_session.get('longName', 'N/A')}")
        else:
            print("   Result: None or empty")
            
        print("   Testing .info with session:")
        info_session = ticker_with_session.info
        print(f"   Type: {type(info_session)}")
        print(f"   Length: {len(info_session) if info_session else 0}")
        if info_session:
            print(f"   Company: {info_session.get('longName', 'N/A')}")
        else:
            print("   Result: None or empty")
            
    except Exception as e:
        print(f"   Error with session: {e}")

if __name__ == "__main__":
    debug_yfinance_methods()
